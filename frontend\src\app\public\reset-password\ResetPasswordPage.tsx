import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Input, 
  Button, 
  Progress,
  Link
} from '@heroui/react'
import { Eye, EyeOff, Lock, CheckCircle, XCircle, AlertTriangle } from 'lucide-react'
import { AuthLayout } from '@/components/layout/AuthLayout'

export const ResetPasswordPage = () => {
  const { token } = useParams<{ token: string }>()
  const navigate = useNavigate()
  
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [isPasswordVisible, setIsPasswordVisible] = useState(false)
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [tokenStatus, setTokenStatus] = useState<'validating' | 'valid' | 'invalid' | 'expired'>('validating')
  const [isSuccess, setIsSuccess] = useState(false)

  // Password strength calculation (same as register page)
  const getPasswordStrength = (password: string) => {
    let strength = 0
    if (password.length >= 8) strength += 25
    if (/[a-z]/.test(password)) strength += 25
    if (/[A-Z]/.test(password)) strength += 25
    if (/[0-9]/.test(password) || /[^A-Za-z0-9]/.test(password)) strength += 25
    return strength
  }

  const passwordStrength = getPasswordStrength(password)
  const getStrengthColor = (strength: number) => {
    if (strength < 25) return 'danger'
    if (strength < 50) return 'warning' 
    if (strength < 75) return 'success'
    return 'primary'
  }
  const getStrengthLabel = (strength: number) => {
    if (strength < 25) return 'Weak'
    if (strength < 50) return 'Fair'
    if (strength < 75) return 'Good'
    return 'Strong'
  }

  const togglePasswordVisibility = () => setIsPasswordVisible(!isPasswordVisible)
  const toggleConfirmPasswordVisibility = () => setIsConfirmPasswordVisible(!isConfirmPasswordVisible)

  // Simulate token validation on mount
  useEffect(() => {
    if (!token) {
      setTokenStatus('invalid')
      return
    }

    // TODO: Implement actual token validation API call
    setTimeout(() => {
      // Simulate different token states for demo
      const random = Math.random()
      if (random < 0.1) {
        setTokenStatus('expired')
      } else if (random < 0.2) {
        setTokenStatus('invalid')
      } else {
        setTokenStatus('valid')
      }
    }, 1500)
  }, [token])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (password !== confirmPassword) return
    
    setIsLoading(true)
    
    // TODO: Implement reset password API call
    setTimeout(() => {
      setIsLoading(false)
      setIsSuccess(true)
      // Auto-redirect to login after 3 seconds
      setTimeout(() => {
        navigate('/login')
      }, 3000)
    }, 2000)
  }

  // Token validation loading state
  if (tokenStatus === 'validating') {
    return (
      <AuthLayout>
        <Card className="w-full max-w-md">
          <CardHeader className="flex flex-col gap-3 pb-6 pt-8">
            <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-2">
              <Lock className="h-8 w-8 text-primary animate-pulse" />
            </div>
            <h1 className="text-2xl font-bold text-center w-full">Validating Token</h1>
            <p className="text-sm text-default-600 text-center">
              Please wait while we validate your reset token...
            </p>
          </CardHeader>
          <CardBody className="gap-4">
            <div className="flex justify-center">
              <Progress 
                isIndeterminate 
                size="sm" 
                className="w-full"
                color="primary"
              />
            </div>
          </CardBody>
        </Card>
      </AuthLayout>
    )
  }

  // Invalid token state
  if (tokenStatus === 'invalid') {
    return (
      <AuthLayout>
        <Card className="w-full max-w-md">
          <CardHeader className="flex flex-col gap-3 pb-6 pt-8">
            <div className="mx-auto w-16 h-16 bg-danger/10 rounded-full flex items-center justify-center mb-2">
              <XCircle className="h-8 w-8 text-danger" />
            </div>
            <h1 className="text-2xl font-bold text-center w-full">Invalid Token</h1>
            <p className="text-sm text-default-600 text-center">
              This password reset link is invalid or has been tampered with.
            </p>
          </CardHeader>
          <CardBody className="gap-4">
            <div className="text-center space-y-4">
              <p className="text-small text-default-600">
                Please request a new password reset link to continue.
              </p>
              
              <Button
                as={Link}
                href="/forgot-password"
                color="primary"
                size="lg"
                className="w-full"
              >
                Request New Reset Link
              </Button>

              <Link 
                href="/login"
                color="primary" 
                className="flex items-center justify-center gap-2 hover:opacity-80"
              >
                Back to Sign In
              </Link>
            </div>
          </CardBody>
        </Card>
      </AuthLayout>
    )
  }

  // Expired token state
  if (tokenStatus === 'expired') {
    return (
      <AuthLayout>
        <Card className="w-full max-w-md">
          <CardHeader className="flex flex-col gap-3 pb-6 pt-8">
            <div className="mx-auto w-16 h-16 bg-warning/10 rounded-full flex items-center justify-center mb-2">
              <AlertTriangle className="h-8 w-8 text-warning" />
            </div>
            <h1 className="text-2xl font-bold text-center w-full">Token Expired</h1>
            <p className="text-sm text-default-600 text-center">
              This password reset link has expired for security reasons.
            </p>
          </CardHeader>
          <CardBody className="gap-4">
            <div className="text-center space-y-4">
              <p className="text-small text-default-600">
                Password reset links expire after 24 hours. Please request a new one.
              </p>
              
              <Button
                as={Link}
                href="/forgot-password"
                color="primary"
                size="lg"
                className="w-full"
              >
                Request New Reset Link
              </Button>

              <Link 
                href="/login"
                color="primary" 
                className="flex items-center justify-center gap-2 hover:opacity-80"
              >
                Back to Sign In
              </Link>
            </div>
          </CardBody>
        </Card>
      </AuthLayout>
    )
  }

  // Success state
  if (isSuccess) {
    return (
      <AuthLayout>
        <Card className="w-full max-w-md">
          <CardHeader className="flex flex-col gap-3 pb-6 pt-8">
            <div className="mx-auto w-16 h-16 bg-success/10 rounded-full flex items-center justify-center mb-2">
              <CheckCircle className="h-8 w-8 text-success" />
            </div>
            <h1 className="text-2xl font-bold text-center w-full">Password Reset!</h1>
            <p className="text-sm text-default-600 text-center">
              Your password has been successfully reset.
            </p>
          </CardHeader>
          <CardBody className="gap-4">
            <div className="text-center space-y-4">
              <p className="text-small text-default-600">
                You will be redirected to the sign in page in a few seconds...
              </p>
              
              <Button
                as={Link}
                href="/login"
                color="primary"
                size="lg"
                className="w-full"
              >
                Sign In Now
              </Button>
            </div>
          </CardBody>
        </Card>
      </AuthLayout>
    )
  }

  // Main reset password form
  return (
    <AuthLayout>
      <Card className="w-full max-w-md">
        <CardHeader className="flex flex-col gap-3 pb-6 pt-8">
          <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-2">
            <Lock className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-2xl font-bold text-center w-full">Reset Password</h1>
          <p className="text-sm text-default-600 text-center">
            Choose a new password for your account
          </p>
        </CardHeader>
        <CardBody className="gap-4">
          <form onSubmit={handleSubmit} className="flex flex-col gap-4">
            {/* New Password Input */}
            <div className="flex flex-col gap-2">
              <Input
                type={isPasswordVisible ? 'text' : 'password'}
                label="New Password"
                placeholder="Create a strong password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                startContent={<Lock className="h-4 w-4 text-default-400" />}
                endContent={
                  <Button
                    className="focus:outline-none"
                    type="button"
                    onPress={togglePasswordVisibility}
                    size="sm"
                    variant="light"
                    isIconOnly
                  >
                    {isPasswordVisible ? (
                      <EyeOff className="h-4 w-4 text-default-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-default-400" />
                    )}
                  </Button>
                }
                classNames={{
                  input: 'bg-transparent',
                  inputWrapper: 'bg-default-100/60 backdrop-blur-md'
                }}
                required
              />
              
              {/* Password Strength Meter */}
              {password && (
                <div className="flex flex-col gap-1">
                  <div className="flex items-center justify-between">
                    <span className="text-tiny text-default-600">Password strength</span>
                    <span className={`text-tiny text-${getStrengthColor(passwordStrength)}`}>
                      {getStrengthLabel(passwordStrength)}
                    </span>
                  </div>
                  <Progress 
                    value={passwordStrength} 
                    color={getStrengthColor(passwordStrength)}
                    size="sm"
                    className="w-full"
                  />
                </div>
              )}
            </div>

            {/* Confirm Password Input */}
            <Input
              type={isConfirmPasswordVisible ? 'text' : 'password'}
              label="Confirm Password"
              placeholder="Confirm your new password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              startContent={<Lock className="h-4 w-4 text-default-400" />}
              endContent={
                <Button
                  className="focus:outline-none"
                  type="button"
                  onPress={toggleConfirmPasswordVisibility}
                  size="sm"
                  variant="light"
                  isIconOnly
                >
                  {isConfirmPasswordVisible ? (
                    <EyeOff className="h-4 w-4 text-default-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-default-400" />
                  )}
                </Button>
              }
              classNames={{
                input: 'bg-transparent',
                inputWrapper: 'bg-default-100/60 backdrop-blur-md'
              }}
              isInvalid={confirmPassword !== '' && password !== confirmPassword}
              errorMessage={
                confirmPassword !== '' && password !== confirmPassword 
                  ? "Passwords don't match" 
                  : ""
              }
              required
            />

            <Button
              type="submit"
              color="primary"
              size="lg"
              className="w-full font-semibold"
              isLoading={isLoading}
              isDisabled={!password || !confirmPassword || password !== confirmPassword}
            >
              {isLoading ? 'Resetting Password...' : 'Reset Password'}
            </Button>
          </form>

          <div className="text-center pt-4 border-t border-default-200">
            <Link 
              href="/login"
              color="primary" 
              className="flex items-center justify-center gap-2 hover:opacity-80"
            >
              Back to Sign In
            </Link>
          </div>
        </CardBody>
      </Card>
    </AuthLayout>
  )
}