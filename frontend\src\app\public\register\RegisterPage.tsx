import { useState } from "react";
import {
  <PERSON>,
  CardBody,
  CardHeader,
  Input,
  Button,
  Link,
  Divider,
  Progress,
} from "@heroui/react";
import { Eye, EyeOff, Mail, Lock, User, Chrome, Shield } from "lucide-react";
import { AuthLayout } from "@/components/layout/AuthLayout";

export const RegisterPage = () => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] =
    useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const togglePasswordVisibility = () =>
    setIsPasswordVisible(!isPasswordVisible);
  const toggleConfirmPasswordVisibility = () =>
    setIsConfirmPasswordVisible(!isConfirmPasswordVisible);

  // Simple password strength calculation
  const getPasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength += 25;
    if (/[a-z]/.test(password)) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password) || /[^A-Za-z0-9]/.test(password)) strength += 25;
    return strength;
  };

  const passwordStrength = getPasswordStrength(password);
  const getStrengthColor = (strength: number) => {
    if (strength < 25) return "danger";
    if (strength < 50) return "warning";
    if (strength < 75) return "success";
    return "primary";
  };
  const getStrengthLabel = (strength: number) => {
    if (strength < 25) return "Weak";
    if (strength < 50) return "Fair";
    if (strength < 75) return "Good";
    return "Strong";
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    // TODO: Implement register logic
    setTimeout(() => setIsLoading(false), 2000); // Simulate API call
  };

  const handleGoogleRegister = () => {
    // TODO: Implement Google OAuth
    console.log("Google register clicked");
  };

  return (
    <AuthLayout>
      <Card className="w-full max-w-lg">
        <CardHeader className="flex flex-col gap-3 pb-6 pt-8">
          {/* Icon */}
          <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-2">
            <Shield className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-2xl font-bold text-center w-full">
            Create Account
          </h1>
          <p className="text-sm text-default-600 text-center">
            Join us today! Create your account to get started
          </p>
        </CardHeader>
        <CardBody className="gap-4">
          <form onSubmit={handleRegister} className="flex flex-col gap-4">
            {/* Name Fields */}
            <div className="flex flex-col sm:flex-row gap-2">
              <Input
                type="text"
                label="First Name"
                placeholder="Enter first name"
                startContent={<User className="h-4 w-4 text-default-400" />}
                classNames={{
                  input: "bg-transparent",
                  inputWrapper: "bg-default-100/60 backdrop-blur-md",
                }}
                required
              />
              <Input
                type="text"
                label="Last Name"
                placeholder="Enter last name"
                startContent={<User className="h-4 w-4 text-default-400" />}
                classNames={{
                  input: "bg-transparent",
                  inputWrapper: "bg-default-100/60 backdrop-blur-md",
                }}
                required
              />
            </div>

            {/* Email Input */}
            <Input
              type="email"
              label="Email"
              placeholder="Enter your email"
              startContent={<Mail className="h-4 w-4 text-default-400" />}
              classNames={{
                input: "bg-transparent",
                inputWrapper: "bg-default-100/60 backdrop-blur-md",
              }}
              required
            />

            {/* Password Input */}
            <div className="flex flex-col gap-2">
              <Input
                type={isPasswordVisible ? "text" : "password"}
                label="Password"
                placeholder="Create a password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                startContent={<Lock className="h-4 w-4 text-default-400" />}
                endContent={
                  <Button
                    className="focus:outline-none"
                    type="button"
                    onPress={togglePasswordVisibility}
                    size="sm"
                    variant="light"
                    isIconOnly
                  >
                    {isPasswordVisible ? (
                      <EyeOff className="h-4 w-4 text-default-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-default-400" />
                    )}
                  </Button>
                }
                classNames={{
                  input: "bg-transparent",
                  inputWrapper: "bg-default-100/60 backdrop-blur-md",
                }}
                required
              />

              {/* Password Strength Meter */}
              {password && (
                <div className="flex flex-col gap-1">
                  <div className="flex items-center justify-between">
                    <span className="text-tiny text-default-600">
                      Password strength
                    </span>
                    <span
                      className={`text-tiny text-${getStrengthColor(
                        passwordStrength
                      )}`}
                    >
                      {getStrengthLabel(passwordStrength)}
                    </span>
                  </div>
                  <Progress
                    value={passwordStrength}
                    color={getStrengthColor(passwordStrength)}
                    size="sm"
                    className="w-full"
                  />
                </div>
              )}
            </div>

            {/* Confirm Password Input */}
            <Input
              type={isConfirmPasswordVisible ? "text" : "password"}
              label="Confirm Password"
              placeholder="Confirm your password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              startContent={<Lock className="h-4 w-4 text-default-400" />}
              endContent={
                <Button
                  className="focus:outline-none"
                  type="button"
                  onPress={toggleConfirmPasswordVisibility}
                  size="sm"
                  variant="light"
                  isIconOnly
                >
                  {isConfirmPasswordVisible ? (
                    <EyeOff className="h-4 w-4 text-default-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-default-400" />
                  )}
                </Button>
              }
              classNames={{
                input: "bg-transparent",
                inputWrapper: "bg-default-100/60 backdrop-blur-md",
              }}
              isInvalid={confirmPassword !== "" && password !== confirmPassword}
              errorMessage={
                confirmPassword !== "" && password !== confirmPassword
                  ? "Passwords don't match"
                  : ""
              }
              required
            />

            {/* Create Account Button */}
            <Button
              type="submit"
              color="primary"
              size="lg"
              className="w-full font-semibold"
              isLoading={isLoading}
            >
              {isLoading ? "Creating Account..." : "Create Account"}
            </Button>
          </form>

          {/* Divider */}
          <div className="flex items-center gap-4 py-2">
            <Divider className="flex-1" />
            <span className="text-small text-default-400">or</span>
            <Divider className="flex-1" />
          </div>

          {/* Google OAuth Button */}
          <Button
            variant="bordered"
            size="lg"
            className="w-full font-semibold"
            startContent={<Chrome className="h-5 w-5" />}
            onPress={handleGoogleRegister}
          >
            Continue with Google
          </Button>

          {/* Sign In Link */}
          <div className="text-center pt-4">
            <span className="text-small text-default-600">
              Already have an account?{" "}
              <Link
                href="/login"
                color="primary"
                className="font-semibold hover:opacity-80"
              >
                Sign in
              </Link>
            </span>
          </div>
        </CardBody>
      </Card>
    </AuthLayout>
  );
};
