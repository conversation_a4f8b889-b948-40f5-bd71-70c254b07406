import { useState } from 'react'
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Input, 
  Button, 
  Checkbox, 
  Link,
  Divider
} from '@heroui/react'
import { Eye, EyeOff, Mail, Lock, Chrome, Shield } from 'lucide-react'
import { AuthLayout } from '@/components/layout/AuthLayout'

export const LoginPage = () => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const togglePasswordVisibility = () => setIsPasswordVisible(!isPasswordVisible)

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    // TODO: Implement login logic
    setTimeout(() => setIsLoading(false), 2000) // Simulate API call
  }

  const handleGoogleLogin = () => {
    // TODO: Implement Google OAuth
    console.log('Google login clicked')
  }

  return (
    <AuthLayout>
      <Card className="w-full max-w-md">
        <CardHeader className="flex flex-col gap-3 pb-6 pt-8">
          {/* Icon */}
          <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-2">
            <Shield className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-2xl font-bold text-center w-full">Sign In</h1>
          <p className="text-sm text-default-600 text-center">
            Welcome back! Please sign in to your account
          </p>
        </CardHeader>
        <CardBody className="gap-4">
          <form onSubmit={handleLogin} className="flex flex-col gap-4">
            {/* Email Input */}
            <Input
              type="email"
              label="Email"
              placeholder="Enter your email"
              startContent={<Mail className="h-4 w-4 text-default-400" />}
              classNames={{
                input: 'bg-transparent',
                inputWrapper: 'bg-default-100/60 backdrop-blur-md'
              }}
              required
            />

            {/* Password Input */}
            <Input
              type={isPasswordVisible ? 'text' : 'password'}
              label="Password"
              placeholder="Enter your password"
              startContent={<Lock className="h-4 w-4 text-default-400" />}
              endContent={
                <Button
                  className="focus:outline-none"
                  type="button"
                  onPress={togglePasswordVisibility}
                  size="sm"
                  variant="light"
                  isIconOnly
                >
                  {isPasswordVisible ? (
                    <EyeOff className="h-4 w-4 text-default-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-default-400" />
                  )}
                </Button>
              }
              classNames={{
                input: 'bg-transparent',
                inputWrapper: 'bg-default-100/60 backdrop-blur-md'
              }}
              required
            />

            {/* Remember Me & Forgot Password */}
            <div className="flex items-center justify-between">
              <Checkbox size="sm" color="primary">
                Remember me
              </Checkbox>
              <Link 
                href="/forgot-password" 
                size="sm" 
                color="primary"
                className="hover:opacity-80"
              >
                Forgot password?
              </Link>
            </div>

            {/* Sign In Button */}
            <Button
              type="submit"
              color="primary"
              size="lg"
              className="w-full font-semibold"
              isLoading={isLoading}
            >
              {isLoading ? 'Signing In...' : 'Sign In'}
            </Button>
          </form>

          {/* Divider */}
          <div className="flex items-center gap-4 py-2">
            <Divider className="flex-1" />
            <span className="text-small text-default-400">or</span>
            <Divider className="flex-1" />
          </div>

          {/* Google OAuth Button */}
          <Button
            variant="bordered"
            size="lg"
            className="w-full font-semibold"
            startContent={<Chrome className="h-5 w-5" />}
            onPress={handleGoogleLogin}
          >
            Continue with Google
          </Button>

          {/* Sign Up Link */}
          <div className="text-center pt-4">
            <span className="text-small text-default-600">
              Don't have an account?{' '}
              <Link 
                href="/register" 
                color="primary"
                className="font-semibold hover:opacity-80"
              >
                Sign up
              </Link>
            </span>
          </div>
        </CardBody>
      </Card>
    </AuthLayout>
  )
}