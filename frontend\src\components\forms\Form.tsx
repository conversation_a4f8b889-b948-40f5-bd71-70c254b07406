import type { ReactNode } from "react";
import {
  FormProvider,
  useForm,
  type SubmitHandler,
  type UseFormProps,
} from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
// import * as z4 from "zod/v4/core";
import { registerSchema } from "@/lib/validation";
import type { z, ZodType } from "zod";

function inferSchema<T extends ZodType>(
  schema: T,
  defaultValues?: z.infer<T>
) {
  return { schema, defaultValues };
}
inferSchema(registerSchema, {
  confirmPassword: "",
  email: "",
  password: "",
  firstName: "",
  lastName: "",
});
export type Test = z.infer<typeof registerSchema>;
type Props<T extends ZodType> = {
  schema: T;
  onSubmit: SubmitHandler<z.infer<T>>;
  children: ReactNode;
  className?: string;
} & Omit<UseFormProps<z.infer<T>>, "resolver">;

export const Form = <T extends ZodType>({
  schema,
  onSubmit,
  children,
  className,
  ...formProps
}: Props<T>) => {
  const form = useForm({
    resolver: zodResolver(schema),
    mode: "onChange",
    ...formProps,
  });

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className={className}>
        {children}
      </form>
    </FormProvider>
  );
};

   <Form
schema={registerSchema}
onSubmit={(data) => console.log(data)}
defaultValues={{
  confirmPassword: 1
}}
>test</Form> 
