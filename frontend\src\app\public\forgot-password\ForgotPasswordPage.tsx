import { useState } from 'react'
import { 
  <PERSON>, 
  CardBody, 
  CardHeader, 
  Input, 
  <PERSON><PERSON>, 
  <PERSON>
} from '@heroui/react'
import { Mail, ArrowLeft, CheckCircle, Clock } from 'lucide-react'
import { AuthLayout } from '@/components/layout/AuthLayout'

export const ForgotPasswordPage = () => {
  const [email, setEmail] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isEmailSent, setIsEmailSent] = useState(false)
  const [cooldownTime, setCooldownTime] = useState(0)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    
    // TODO: Implement forgot password API call
    setTimeout(() => {
      setIsLoading(false)
      setIsEmailSent(true)
      // Start 60-second cooldown for resend
      setCooldownTime(60)
      const timer = setInterval(() => {
        setCooldownTime((prev) => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }, 2000)
  }

  const handleResend = () => {
    if (cooldownTime > 0) return
    
    setIsLoading(true)
    // TODO: Implement resend API call
    setTimeout(() => {
      setIsLoading(false)
      setCooldownTime(60)
      const timer = setInterval(() => {
        setCooldownTime((prev) => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }, 1000)
  }

  if (isEmailSent) {
    return (
      <AuthLayout>
        <Card className="w-full max-w-md">
          <CardHeader className="flex flex-col gap-3 pb-6 pt-8">
            {/* Success Icon */}
            <div className="mx-auto w-16 h-16 bg-success/10 rounded-full flex items-center justify-center mb-2">
              <CheckCircle className="h-8 w-8 text-success" />
            </div>
            <h1 className="text-2xl font-bold text-center w-full">Check Your Email</h1>
            <p className="text-sm text-default-600 text-center">
              We've sent a password reset link to <strong>{email}</strong>
            </p>
          </CardHeader>
          <CardBody className="gap-4">
            <div className="text-center space-y-4">
              <p className="text-small text-default-600">
                Didn't receive the email? Check your spam folder or click below to resend.
              </p>
              
              <Button
                variant="bordered"
                size="lg"
                className="w-full"
                onPress={handleResend}
                isLoading={isLoading}
                isDisabled={cooldownTime > 0}
                startContent={cooldownTime > 0 ? <Clock className="h-4 w-4" /> : undefined}
              >
                {isLoading 
                  ? 'Sending...' 
                  : cooldownTime > 0 
                    ? `Resend in ${cooldownTime}s` 
                    : 'Resend Email'
                }
              </Button>

              <div className="pt-4 border-t border-default-200">
                <Link 
                  href="/login"
                  color="primary" 
                  className="flex items-center justify-center gap-2 hover:opacity-80"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back to Sign In
                </Link>
              </div>
            </div>
          </CardBody>
        </Card>
      </AuthLayout>
    )
  }

  return (
    <AuthLayout>
      <Card className="w-full max-w-md">
        <CardHeader className="flex flex-col gap-3 pb-6 pt-8">
          {/* Icon */}
          <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-2">
            <Mail className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-2xl font-bold text-center w-full">Forgot Password?</h1>
          <p className="text-sm text-default-600 text-center">
            No worries! Enter your email address and we'll send you a reset link.
          </p>
        </CardHeader>
        <CardBody className="gap-4">
          <form onSubmit={handleSubmit} className="flex flex-col gap-4">
            <Input
              type="email"
              label="Email"
              placeholder="Enter your email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              startContent={<Mail className="h-4 w-4 text-default-400" />}
              classNames={{
                input: 'bg-transparent',
                inputWrapper: 'bg-default-100/60 backdrop-blur-md'
              }}
              required
            />

            <Button
              type="submit"
              color="primary"
              size="lg"
              className="w-full font-semibold"
              isLoading={isLoading}
            >
              {isLoading ? 'Sending Reset Link...' : 'Send Reset Link'}
            </Button>
          </form>

          <div className="text-center pt-4 border-t border-default-200">
            <span className="text-small text-default-600">
              Remember your password?{' '}
              <Link 
                href="/login" 
                color="primary"
                className="font-semibold hover:opacity-80"
              >
                Sign in
              </Link>
            </span>
          </div>
        </CardBody>
      </Card>
    </AuthLayout>
  )
}